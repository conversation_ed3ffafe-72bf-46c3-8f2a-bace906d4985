.demo-stepper-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  margin-bottom: 30px;
  text-align: center;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 10px;
  }

  p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 20px;
  }
}

.status-info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.pending {
    background-color: #fef3c7;
    color: #92400e;
  }

  &.in_progress {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.completed {
    background-color: #dcfce7;
    color: #166534;
  }

  &.failed {
    background-color: #fecaca;
    color: #dc2626;
  }
}

.progress-info {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.demo-controls {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 30px;
}

.control-btn {
  padding: 8px 16px;
  border: 2px solid;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;

  &.restart {
    border-color: #3b82f6;
    color: #3b82f6;

    &:hover {
      background-color: #3b82f6;
      color: white;
    }
  }

  &.pause {
    border-color: #f59e0b;
    color: #f59e0b;

    &:hover {
      background-color: #f59e0b;
      color: white;
    }
  }

  &.resume {
    border-color: #10b981;
    color: #10b981;

    &:hover {
      background-color: #10b981;
      color: white;
    }
  }
}

.stepper-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 30px;
  margin-bottom: 30px;
}

.demo-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 15px;
  }

  ul {
    list-style: none;
    padding-left: 0;
    color: #475569;

    li {
      margin-bottom: 8px;
      line-height: 1.5;
      padding-left: 0;
    }
  }
}

.api-response-sample {
  background: #1e293b;
  border-radius: 8px;
  padding: 20px;
  overflow-x: auto;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 15px;
  }

  pre {
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;

    code {
      color: #e2e8f0;
      background: none;
      padding: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-stepper-container {
    padding: 15px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .status-info {
    flex-direction: column;
    gap: 10px;
  }

  .demo-controls {
    flex-direction: column;
    align-items: center;
  }

  .stepper-container {
    padding: 20px;
  }
}
