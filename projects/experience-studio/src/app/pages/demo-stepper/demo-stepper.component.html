<div class="demo-stepper-container">
  <div class="header">
    <h1>Demo Stepper with File Accordion</h1>
    <p>This demonstrates the vertical stepper component with file accordion functionality.</p>
    
    <div class="status-info">
      <span class="status-badge" [ngClass]="status.toLowerCase()">{{ status }}</span>
      <span class="progress-info">Current Progress: {{ progress }}</span>
    </div>

    <div class="demo-controls">
      <button class="control-btn restart" (click)="restartDemo()">🔄 Restart Demo</button>
      <button class="control-btn pause" (click)="pauseDemo()">⏸️ Pause</button>
      <button class="control-btn resume" (click)="resumeDemo()">▶️ Resume</button>
    </div>
  </div>

  <div class="stepper-container">
    <app-vertical-stepper
      [progress]="progress"
      [progressDescription]="progressDescription"
      [status]="status"
      [theme]="'light'"
      [restartable]="false"
      [useApi]="false"
      (stepUpdated)="onStepUpdated($event)"
      (retryStep)="onRetryStep($event)"
      (fileClicked)="onFileClicked($event)"
      (versionRestored)="onVersionRestored($event)"
      (versionViewed)="onVersionViewed($event)">
    </app-vertical-stepper>
  </div>

  <div class="demo-info">
    <h3>Demo Features:</h3>
    <ul>
      <li>✅ Auto-progressing stepper (every 4 seconds)</li>
      <li>✅ File accordion appears in "Components Created" and "Pages Generated" steps</li>
      <li>✅ Interactive file clicking with details</li>
      <li>✅ Restore and View version buttons</li>
      <li>✅ Different file statuses: Generated, Created, Modified</li>
      <li>✅ Demo controls: Restart, Pause, Resume</li>
    </ul>
  </div>

  <div class="api-response-sample">
    <h3>Sample API Response Format:</h3>
    <pre><code>{{getSampleApiResponse()}}</code></pre>
  </div>
</div>
