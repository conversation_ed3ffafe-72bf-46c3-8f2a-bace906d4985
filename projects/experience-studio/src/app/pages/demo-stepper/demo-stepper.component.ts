import { Component, OnInit, ViewChild, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { CommonModule } from '@angular/common';
import { VerticalStepperComponent, StepperItem } from '../../shared/components/vertical-stepper/vertical-stepper.component';
import { GeneratedFile } from '../../shared/models/stepper-states.enum';
import { FileAccordionVersion } from '../../shared/components/file-accordion/file-accordion.component';

@Component({
  selector: 'app-demo-stepper',
  standalone: true,
  imports: [CommonModule, VerticalStepperComponent],
  templateUrl: './demo-stepper.component.html',
  styleUrls: ['./demo-stepper.component.scss']
})
export class DemoStepperComponent implements OnInit, OnDestroy {
  @ViewChild(VerticalStepperComponent) stepper!: VerticalStepperComponent;

  currentStepIndex = 0;
  status = 'IN_PROGRESS';
  progress = 'OVERVIEW';
  progressDescription = 'Setting up the project structure and initial configuration.';

  private progressInterval: any;

  // Sample files for different steps
  private sampleFiles: { [key: string]: GeneratedFile[] } = {
    'COMPONENTS_CREATED': [
      {
        fileName: 'app/page.tsx',
        status: 'generated',
        path: 'src/app/page.tsx',
        content: 'export default function Page() { return <div>Hello World</div>; }'
      },
      {
        fileName: 'app/loading.tsx',
        status: 'generated',
        path: 'src/app/loading.tsx',
        content: 'export default function Loading() { return <div>Loading...</div>; }'
      },
      {
        fileName: 'components/NavigationMenu.tsx',
        status: 'created',
        path: 'src/components/NavigationMenu.tsx',
        content: 'export default function NavigationMenu() { return <nav>Navigation</nav>; }'
      },
      {
        fileName: 'components/CategoryScroller.tsx',
        status: 'created',
        path: 'src/components/CategoryScroller.tsx',
        content: 'export default function CategoryScroller() { return <div>Categories</div>; }'
      },
      {
        fileName: 'components/OfferBannerCard.tsx',
        status: 'created',
        path: 'src/components/OfferBannerCard.tsx',
        content: 'export default function OfferBannerCard() { return <div>Offer Banner</div>; }'
      }
    ],
    'PAGES_GENERATED': [
      {
        fileName: 'pages/HomePage.tsx',
        status: 'generated',
        path: 'src/pages/HomePage.tsx',
        content: 'export default function HomePage() { return <div>Home Page</div>; }'
      },
      {
        fileName: 'pages/ProductPage.tsx',
        status: 'generated',
        path: 'src/pages/ProductPage.tsx',
        content: 'export default function ProductPage() { return <div>Product Page</div>; }'
      },
      {
        fileName: 'pages/CartPage.tsx',
        status: 'modified',
        path: 'src/pages/CartPage.tsx',
        content: 'export default function CartPage() { return <div>Cart Page</div>; }'
      }
    ],
    'BUILD_SUCCEEDED': [
      {
        fileName: 'dist/index.html',
        status: 'generated',
        path: 'dist/index.html',
        content: '<!DOCTYPE html><html><head><title>App</title></head><body><div id="root"></div></body></html>'
      },
      {
        fileName: 'dist/main.js',
        status: 'generated',
        path: 'dist/main.js',
        content: '// Compiled JavaScript bundle'
      },
      {
        fileName: 'dist/styles.css',
        status: 'generated',
        path: 'dist/styles.css',
        content: '/* Compiled CSS styles */'
      }
    ]
  };

  ngOnInit(): void {
    this.startDemo();
  }

  ngOnDestroy(): void {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }
  }

  private startDemo(): void {
    // Initialize with first step
    this.updateStepperProgress();

    // Auto-progress through steps
    this.progressInterval = setInterval(() => {
      this.nextStep();
    }, 4000);
  }

  private nextStep(): void {
    const steps = ['OVERVIEW', 'SEED_PROJECT_INITIALIZED', 'COMPONENTS_CREATED', 'DESIGN_SYSTEM_MAPPED', 'PAGES_GENERATED', 'BUILD_STARTED', 'BUILD_SUCCEEDED'];
    const descriptions = [
      'Setting up the project structure and initial configuration.',
      'Seed project has been initialized with base dependencies.',
      'I have created the base files for the NavigationMenu, CategoryScroller, OfferBannerCard, ProductDiscoveryCard, Footer, Sidebar, and Carousel components as requested.',
      'Design system components and styles have been mapped.',
      'Generated the Homepage, ProductListingPage, ProductDetailPage, CartPage, and AccountPage components based on your descriptions.',
      'Starting the build process...',
      'Build created successfully. Please wait for a few minutes to view your preview.'
    ];

    if (this.currentStepIndex < steps.length - 1) {
      this.currentStepIndex++;
      this.progress = steps[this.currentStepIndex];
      this.progressDescription = descriptions[this.currentStepIndex];

      if (this.currentStepIndex === steps.length - 1) {
        this.status = 'COMPLETED';
      }

      this.updateStepperProgress();
    } else {
      // Reset demo
      this.resetDemo();
    }
  }

  private resetDemo(): void {
    this.currentStepIndex = 0;
    this.status = 'IN_PROGRESS';
    this.progress = 'OVERVIEW';
    this.progressDescription = 'Setting up the project structure and initial configuration.';

    // Reset the stepper
    if (this.stepper) {
      this.stepper.resetStepper();
    }

    setTimeout(() => {
      this.updateStepperProgress();
    }, 1000);
  }

  private updateStepperProgress(): void {
    // Update the stepper with current progress
    if (this.stepper) {
      this.stepper.progress = this.progress;
      this.stepper.progressDescription = this.progressDescription;
      this.stepper.status = this.status;

      // Manually trigger the stepper update
      this.stepper.ngOnChanges({
        progress: { currentValue: this.progress, previousValue: '', firstChange: false, isFirstChange: () => false },
        progressDescription: { currentValue: this.progressDescription, previousValue: '', firstChange: false, isFirstChange: () => false },
        status: { currentValue: this.status, previousValue: '', firstChange: false, isFirstChange: () => false }
      });
    }
  }

  onStepUpdated(stepIndex: number): void {
    console.log('Step updated:', stepIndex);
  }

  onRetryStep(stepIndex: number): void {
    console.log('Retry step:', stepIndex);
    alert('Retry functionality triggered for step: ' + stepIndex);
  }

  onFileClicked(file: GeneratedFile): void {
    console.log('File clicked:', file);
    alert(`File clicked: ${file.fileName}\nStatus: ${file.status}\nPath: ${file.path || 'N/A'}`);
  }

  onVersionRestored(version: FileAccordionVersion): void {
    console.log('Version restored:', version);
    alert(`Version restored: ${version.version}\nFiles: ${version.files.length}`);
  }

  onVersionViewed(version: FileAccordionVersion): void {
    console.log('Version viewed:', version);
    alert(`Version viewed: ${version.version}\nFiles: ${version.files.length}`);
  }

  restartDemo(): void {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }
    this.resetDemo();
    this.startDemo();
  }

  pauseDemo(): void {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }
  }

  resumeDemo(): void {
    if (!this.progressInterval) {
      this.progressInterval = setInterval(() => {
        this.nextStep();
      }, 4000);
    }
  }

  getSampleApiResponse(): string {
    return JSON.stringify({
      "status_code": 200,
      "details": {
        "status": "IN_PROGRESS",
        "log": "",
        "progress": "COMPONENTS_CREATED",
        "progress_description": "I have created the base files for the NavigationMenu, CategoryScroller, OfferBannerCard, ProductDiscoveryCard, Footer, Sidebar, and Carousel components as requested.",
        "files": [
          { "fileName": "app/page.tsx", "status": "generated" },
          { "fileName": "app/loading.tsx", "status": "generated" },
          { "fileName": "components/NavigationMenu.tsx", "status": "created" },
          { "fileName": "components/CategoryScroller.tsx", "status": "created" },
          { "fileName": "components/OfferBannerCard.tsx", "status": "created" }
        ]
      }
    }, null, 2);
  }
}
