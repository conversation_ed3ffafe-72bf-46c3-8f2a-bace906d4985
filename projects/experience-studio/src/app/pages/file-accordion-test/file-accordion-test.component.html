<div class="file-accordion-test-container">
  <div class="header">
    <h1>File Accordion Test</h1>
    <p>This page demonstrates the file accordion implementation in the vertical stepper component.</p>
    <div class="status-info">
      <span class="status-badge" [ngClass]="status.toLowerCase()">{{ status }}</span>
      <span class="progress-info">Current Progress: {{ progress }}</span>
    </div>
  </div>

  <div class="stepper-container">
    <!-- Demo Stepper with Manual Steps -->
    <div class="demo-stepper">
      <app-vertical-stepper
        [theme]="'light'"
        [restartable]="false"
        [useApi]="false"
        (stepUpdated)="onStepUpdated($event)"
        (retryStep)="onRetryStep($event)"
        (fileClicked)="onFileClicked($event)"
        (versionRestored)="onVersionRestored($event)"
        (versionViewed)="onVersionViewed($event)">
      </app-vertical-stepper>
    </div>
  </div>

  <div class="instructions">
    <h3>Instructions:</h3>
    <ul>
      <li>The stepper will automatically progress through steps every 5 seconds</li>
      <li>Click on the "Components Created" step to see the file accordion</li>
      <li>Click on individual files to see file details</li>
      <li>Use the "Restore" and "View" buttons to interact with file versions</li>
      <li>The accordion shows files with different statuses: Generated, Created, Modified</li>
    </ul>
  </div>

  <div class="sample-data">
    <h3>Sample Response Data:</h3>
    <pre><code>{{getSampleResponseData()}}</code></pre>
  </div>
</div>
