.file-accordion-test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  margin-bottom: 30px;
  text-align: center;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 10px;
  }

  p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 20px;
  }
}

.status-info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.pending {
    background-color: #fef3c7;
    color: #92400e;
  }

  &.in_progress {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.completed {
    background-color: #dcfce7;
    color: #166534;
  }

  &.failed {
    background-color: #fecaca;
    color: #dc2626;
  }
}

.progress-info {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.stepper-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 30px;
  margin-bottom: 30px;
}

.instructions {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 15px;
  }

  ul {
    list-style-type: disc;
    padding-left: 20px;
    color: #475569;

    li {
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}

.sample-data {
  background: #1e293b;
  border-radius: 8px;
  padding: 20px;
  overflow-x: auto;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 15px;
  }

  pre {
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;

    code {
      color: #e2e8f0;
      background: none;
      padding: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .file-accordion-test-container {
    padding: 15px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .status-info {
    flex-direction: column;
    gap: 10px;
  }

  .stepper-container {
    padding: 20px;
  }
}
