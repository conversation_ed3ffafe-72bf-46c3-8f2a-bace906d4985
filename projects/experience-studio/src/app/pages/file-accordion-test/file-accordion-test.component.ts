import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { VerticalStepperComponent, StepperItem } from '../../shared/components/vertical-stepper/vertical-stepper.component';
import { GeneratedFile } from '../../shared/models/stepper-states.enum';
import { FileAccordionVersion } from '../../shared/components/file-accordion/file-accordion.component';

@Component({
  selector: 'app-file-accordion-test',
  standalone: true,
  imports: [CommonModule, VerticalStepperComponent],
  templateUrl: './file-accordion-test.component.html',
  styleUrls: ['./file-accordion-test.component.scss']
})
export class FileAccordionTestComponent implements OnInit {
  currentStepIndex = 0;
  status = 'IN_PROGRESS';
  progress = 'COMPONENTS_CREATED';
  progressDescription = 'I have created the base files for the `NavigationMenu`, `CategoryScroller`, `OfferBannerCard`, `ProductDiscoveryCard`, `Footer`, `Sidebar`, and `Carousel` components as requested.';

  // Sample data that matches the user's requirement
  sampleSteps: StepperItem[] = [
    {
      title: 'Project Overview',
      description: 'Setting up the project structure and initial configuration.',
      visibleDescription: 'Setting up the project structure and initial configuration.',
      completed: true,
      active: false,
      startTime: Date.now() - 120000,
      elapsedTime: 120,
      timerActive: false,
      completionTime: 120
    },
    {
      title: 'Seed Project',
      description: 'Initializing the seed project with base dependencies.',
      visibleDescription: 'Initializing the seed project with base dependencies.',
      completed: true,
      active: false,
      startTime: Date.now() - 90000,
      elapsedTime: 90,
      timerActive: false,
      completionTime: 90
    },
    {
      title: 'Components Created',
      description: 'I have created the base files for the `NavigationMenu`, `CategoryScroller`, `OfferBannerCard`, `ProductDiscoveryCard`, `Footer`, `Sidebar`, and `Carousel` components as requested.',
      visibleDescription: 'I have created the base files for the `NavigationMenu`, `CategoryScroller`, `OfferBannerCard`, `ProductDiscoveryCard`, `Footer`, `Sidebar`, and `Carousel` components as requested.',
      completed: false,
      active: true,
      startTime: Date.now() - 30000,
      elapsedTime: 30,
      timerActive: true,
      files: [
        {
          fileName: 'app/page.tsx',
          status: 'generated',
          path: 'src/app/page.tsx',
          content: 'export default function Page() { return <div>Hello World</div>; }'
        },
        {
          fileName: 'app/loading.tsx',
          status: 'generated',
          path: 'src/app/loading.tsx',
          content: 'export default function Loading() { return <div>Loading...</div>; }'
        },
        {
          fileName: 'components/NavigationMenu.tsx',
          status: 'created',
          path: 'src/components/NavigationMenu.tsx',
          content: 'export default function NavigationMenu() { return <nav>Navigation</nav>; }'
        },
        {
          fileName: 'components/CategoryScroller.tsx',
          status: 'created',
          path: 'src/components/CategoryScroller.tsx',
          content: 'export default function CategoryScroller() { return <div>Categories</div>; }'
        },
        {
          fileName: 'components/OfferBannerCard.tsx',
          status: 'created',
          path: 'src/components/OfferBannerCard.tsx',
          content: 'export default function OfferBannerCard() { return <div>Offer Banner</div>; }'
        }
      ]
    },
    {
      title: 'Design System',
      description: 'Mapping design system components and styles.',
      visibleDescription: '',
      completed: false,
      active: false,
      isTyping: false
    }
  ];

  ngOnInit(): void {
    // Simulate the stepper progression
    this.simulateProgress();
  }

  private simulateProgress(): void {
    // Simulate step progression every 5 seconds
    setInterval(() => {
      if (this.currentStepIndex < this.sampleSteps.length - 1) {
        // Complete current step
        this.sampleSteps[this.currentStepIndex].completed = true;
        this.sampleSteps[this.currentStepIndex].active = false;
        this.sampleSteps[this.currentStepIndex].timerActive = false;

        // Move to next step
        this.currentStepIndex++;
        this.sampleSteps[this.currentStepIndex].active = true;
        this.sampleSteps[this.currentStepIndex].timerActive = true;
        this.sampleSteps[this.currentStepIndex].startTime = Date.now();

        // Update progress
        this.updateProgress();
      } else {
        // Complete the process
        this.status = 'COMPLETED';
        this.sampleSteps[this.currentStepIndex].completed = true;
        this.sampleSteps[this.currentStepIndex].active = false;
        this.sampleSteps[this.currentStepIndex].timerActive = false;
      }
    }, 5000);
  }

  private updateProgress(): void {
    const progressStates = ['OVERVIEW', 'SEED_PROJECT_INITIALIZED', 'COMPONENTS_CREATED', 'DESIGN_SYSTEM_MAPPED'];
    const descriptions = [
      'Setting up the project overview.',
      'Seed project has been initialized successfully.',
      'Components have been created and are ready for use.',
      'Design system mapping is complete.'
    ];

    if (this.currentStepIndex < progressStates.length) {
      this.progress = progressStates[this.currentStepIndex];
      this.progressDescription = descriptions[this.currentStepIndex];
    }
  }

  onStepUpdated(stepIndex: number): void {
    console.log('Step updated:', stepIndex);
  }

  onRetryStep(stepIndex: number): void {
    console.log('Retry step:', stepIndex);
  }

  onFileClicked(file: GeneratedFile): void {
    console.log('File clicked:', file);
    alert(`File clicked: ${file.fileName}\nStatus: ${file.status}\nPath: ${file.path}`);
  }

  onVersionRestored(version: FileAccordionVersion): void {
    console.log('Version restored:', version);
    alert(`Version restored: ${version.version}`);
  }

  onVersionViewed(version: FileAccordionVersion): void {
    console.log('Version viewed:', version);
    alert(`Version viewed: ${version.version}`);
  }

  getSampleResponseData(): string {
    return JSON.stringify({
      "status_code": 200,
      "details": {
        "status": "IN_PROGRESS",
        "log": "",
        "progress": "COMPONENTS_CREATED",
        "progress_description": "I have created the base files for the `NavigationMenu`, `CategoryScroller`, `OfferBannerCard`, `ProductDiscoveryCard`, `Footer`, `Sidebar`, and `Carousel` components as requested.",
        "files": [
          { "fileName": "app/page.tsx", "status": "generated" },
          { "fileName": "app/loading.tsx", "status": "generated" },
          { "fileName": "components/NavigationMenu.tsx", "status": "created" },
          { "fileName": "components/CategoryScroller.tsx", "status": "created" },
          { "fileName": "components/OfferBannerCard.tsx", "status": "created" }
        ]
      }
    }, null, 2);
  }
}
