.demos-index-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.header {
  text-align: center;
  margin-bottom: 50px;
  padding: 40px 0;

  h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }
}

.demos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.demo-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #1e293b;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.demo-icon {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 20px;
}

.demo-content {
  h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 12px;
    color: #1e293b;
  }

  p {
    font-size: 1rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 20px;
  }
}

.features-list {
  h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #374151;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      font-size: 0.875rem;
      color: #6b7280;
      margin-bottom: 6px;
      padding-left: 16px;
      position: relative;

      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #10b981;
        font-weight: bold;
      }
    }
  }
}

.demo-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.demo-btn {
  flex: 1;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.875rem;

  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  }

  &.secondary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;

    &:hover {
      background: #667eea;
      color: white;
    }
  }
}

.implementation-info {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 40px;
  margin-bottom: 40px;
  color: #1e293b;
  backdrop-filter: blur(10px);

  h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
    color: #1e293b;
  }
}

.info-section {
  margin-bottom: 40px;

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #374151;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.info-card {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #667eea;

  h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1e293b;
  }

  p {
    font-size: 0.875rem;
    color: #64748b;
    line-height: 1.6;
    margin: 0;
  }
}

.file-structure {
  background: #1e293b;
  border-radius: 8px;
  padding: 20px;
  overflow-x: auto;

  pre {
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;

    code {
      color: #e2e8f0;
      background: none;
      padding: 0;
    }
  }
}

.quick-start {
  ol {
    padding-left: 20px;

    li {
      font-size: 1rem;
      color: #374151;
      margin-bottom: 12px;
      line-height: 1.6;

      code {
        background: #f1f5f9;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.875rem;
        color: #1e293b;
      }
    }
  }
}

.api-response-example {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 30px;
  color: #1e293b;
  backdrop-filter: blur(10px);

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #374151;
  }

  pre {
    background: #1e293b;
    border-radius: 8px;
    padding: 20px;
    overflow-x: auto;
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;

    code {
      color: #e2e8f0;
      background: none;
      padding: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .demos-index-container {
    padding: 15px;
  }

  .header h1 {
    font-size: 2.5rem;
  }

  .demos-grid {
    grid-template-columns: 1fr;
  }

  .demo-actions {
    flex-direction: column;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
