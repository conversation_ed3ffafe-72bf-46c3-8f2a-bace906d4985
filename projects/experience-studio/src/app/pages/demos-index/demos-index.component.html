<div class="demos-index-container">
  <div class="header">
    <h1>File Accordion Implementation Demos</h1>
    <p>Choose from three different implementations to see the file accordion in action</p>
  </div>

  <div class="demos-grid">
    <div class="demo-card" *ngFor="let demo of demos">
      <div class="demo-icon">{{ demo.icon }}</div>
      
      <div class="demo-content">
        <h3>{{ demo.title }}</h3>
        <p>{{ demo.description }}</p>
        
        <div class="features-list">
          <h4>Features:</h4>
          <ul>
            <li *ngFor="let feature of demo.features">{{ feature }}</li>
          </ul>
        </div>
      </div>
      
      <div class="demo-actions">
        <button class="demo-btn primary" (click)="navigateToDemo(demo.route)">
          Open Demo
        </button>
        <a [href]="demo.route" target="_blank" class="demo-btn secondary">
          Open in New Tab
        </a>
      </div>
    </div>
  </div>

  <div class="implementation-info">
    <h2>Implementation Overview</h2>
    
    <div class="info-section">
      <h3>🎯 What's Implemented</h3>
      <div class="info-grid">
        <div class="info-card">
          <h4>File Accordion Component</h4>
          <p>A reusable Angular component that displays files in expandable version groups with status indicators and action buttons.</p>
        </div>
        
        <div class="info-card">
          <h4>Vertical Stepper Integration</h4>
          <p>Enhanced the existing vertical stepper to display file accordions when files are available in step data.</p>
        </div>
        
        <div class="info-card">
          <h4>API Response Compatibility</h4>
          <p>Handles the exact API response format you provided with status, progress, and file information.</p>
        </div>
      </div>
    </div>

    <div class="info-section">
      <h3>📁 File Structure</h3>
      <div class="file-structure">
        <pre><code>src/app/shared/components/
├── file-accordion/
│   ├── file-accordion.component.ts
│   ├── file-accordion.component.html
│   └── file-accordion.component.scss
└── vertical-stepper/
    ├── vertical-stepper.component.ts (enhanced)
    ├── vertical-stepper.component.html (enhanced)
    └── vertical-stepper.component.scss (enhanced)

src/app/shared/models/
└── stepper-states.enum.ts (enhanced with GeneratedFile interface)

src/app/pages/
├── demo-stepper/
├── component-demo/
└── demos-index/</code></pre>
      </div>
    </div>

    <div class="info-section">
      <h3>🚀 Quick Start</h3>
      <div class="quick-start">
        <ol>
          <li><strong>Use in Vertical Stepper:</strong> Add <code>files</code> array to your <code>StepperItem</code></li>
          <li><strong>Standalone Usage:</strong> Import <code>FileAccordionComponent</code> and pass <code>FileAccordionVersion[]</code></li>
          <li><strong>Handle Events:</strong> Listen to <code>fileClick</code>, <code>restoreVersion</code>, and <code>viewVersion</code> events</li>
        </ol>
      </div>
    </div>
  </div>

  <div class="api-response-example">
    <h3>📡 API Response Format</h3>
    <pre><code>{{getApiResponseExample()}}</code></pre>
  </div>
</div>
