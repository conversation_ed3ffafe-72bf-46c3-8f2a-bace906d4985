import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-demos-index',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './demos-index.component.html',
  styleUrls: ['./demos-index.component.scss']
})
export class DemosIndexComponent {
  demos = [
    {
      title: 'Vertical Stepper',
      description: 'The main vertical stepper component with file accordion integration',
      route: '/experience/(primary:prompt-to-code)',
      icon: '📋',
      features: [
        'Real API integration',
        'File accordion display',
        'Progress tracking',
        'Timer functionality',
        'Retry mechanisms'
      ]
    },
    {
      title: 'Demo Stepper',
      description: 'Interactive demonstration of the stepper with auto-progression and file accordion',
      route: '/experience/(primary:demo-stepper)',
      icon: '🎬',
      features: [
        'Auto-progressing steps',
        'File accordion with multiple steps',
        'Demo controls (pause/resume/restart)',
        'Sample file generation',
        'Interactive file clicking'
      ]
    },
    {
      title: 'Component Demo',
      description: 'Standalone file accordion component with all features and controls',
      route: '/experience/(primary:component-demo)',
      icon: '🧩',
      features: [
        'Standalone component showcase',
        'Theme switching (light/dark)',
        'Multiple file versions',
        'Interactive controls',
        'Component API documentation'
      ]
    }
  ];

  navigateToDemo(route: string): void {
    window.open(route, '_blank');
  }

  getApiResponseExample(): string {
    return JSON.stringify({
      "status_code": 200,
      "details": {
        "status": "IN_PROGRESS",
        "log": "",
        "progress": "COMPONENTS_CREATED",
        "progress_description": "I have created the base files for the NavigationMenu, CategoryScroller, OfferBannerCard, ProductDiscoveryCard, Footer, Sidebar, and Carousel components as requested.",
        "files": [
          { "fileName": "app/page.tsx", "status": "generated" },
          { "fileName": "app/loading.tsx", "status": "generated" },
          { "fileName": "components/NavigationMenu.tsx", "status": "created" },
          { "fileName": "components/CategoryScroller.tsx", "status": "created" },
          { "fileName": "components/OfferBannerCard.tsx", "status": "created" }
        ]
      }
    }, null, 2);
  }
}
