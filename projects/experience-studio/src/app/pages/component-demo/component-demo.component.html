<div class="component-demo-container" [ngClass]="currentTheme">
  <div class="header">
    <h1>File Accordion Component Demo</h1>
    <p>Standalone demonstration of the file accordion component with all features.</p>
  </div>

  <div class="controls-section">
    <h3>Component Controls</h3>
    <div class="controls-grid">
      <div class="control-group">
        <label>Theme:</label>
        <button class="control-btn" (click)="toggleTheme()">
          {{ currentTheme === 'light' ? '🌙 Switch to Dark' : '☀️ Switch to Light' }}
        </button>
      </div>
      
      <div class="control-group">
        <label>Actions:</label>
        <button class="control-btn" (click)="toggleActions()">
          {{ showActions ? '🚫 Hide Actions' : '✅ Show Actions' }}
        </button>
      </div>
      
      <div class="control-group">
        <label>Versions:</label>
        <div class="version-controls">
          <button class="control-btn small" (click)="addNewVersion()">➕ Add Version</button>
          <button class="control-btn small" (click)="removeLastVersion()">➖ Remove Version</button>
        </div>
      </div>
      
      <div class="control-group">
        <label>Expand/Collapse:</label>
        <div class="expand-controls">
          <button class="control-btn small" (click)="expandAllVersions()">📂 Expand All</button>
          <button class="control-btn small" (click)="collapseAllVersions()">📁 Collapse All</button>
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Live Component Demo</h3>
    <div class="component-container" [ngClass]="currentTheme">
      <app-file-accordion
        [versions]="fileVersions"
        [theme]="currentTheme"
        [showActions]="showActions"
        (fileClick)="onFileClick($event)"
        (restoreVersion)="onRestoreVersion($event)"
        (viewVersion)="onViewVersion($event)">
      </app-file-accordion>
    </div>
  </div>

  <div class="code-section">
    <h3>Component Usage</h3>
    <div class="code-block">
      <h4>HTML Template:</h4>
      <pre><code>{{getComponentCode()}}</code></pre>
    </div>
    
    <div class="code-block">
      <h4>Data Structure:</h4>
      <pre><code>{{getDataStructure()}}</code></pre>
    </div>
  </div>

  <div class="features-section">
    <h3>Component Features</h3>
    <div class="features-grid">
      <div class="feature-card">
        <h4>🎨 Theming</h4>
        <p>Supports both light and dark themes with CSS custom properties</p>
      </div>
      
      <div class="feature-card">
        <h4>📁 Expandable Versions</h4>
        <p>Click version headers to expand/collapse file lists</p>
      </div>
      
      <div class="feature-card">
        <h4>📄 File Status</h4>
        <p>Visual indicators for Generated, Created, and Modified files</p>
      </div>
      
      <div class="feature-card">
        <h4>⚡ Interactive Actions</h4>
        <p>Restore and View buttons for version management</p>
      </div>
      
      <div class="feature-card">
        <h4>🖱️ File Clicking</h4>
        <p>Click individual files to view details and content</p>
      </div>
      
      <div class="feature-card">
        <h4>📱 Responsive</h4>
        <p>Mobile-friendly design with proper touch interactions</p>
      </div>
    </div>
  </div>

  <div class="api-section">
    <h3>Component API</h3>
    <div class="api-table">
      <table>
        <thead>
          <tr>
            <th>Property</th>
            <th>Type</th>
            <th>Description</th>
            <th>Default</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>versions</code></td>
            <td><code>FileAccordionVersion[]</code></td>
            <td>Array of file versions to display</td>
            <td><code>[]</code></td>
          </tr>
          <tr>
            <td><code>theme</code></td>
            <td><code>'light' | 'dark'</code></td>
            <td>Visual theme of the component</td>
            <td><code>'light'</code></td>
          </tr>
          <tr>
            <td><code>showActions</code></td>
            <td><code>boolean</code></td>
            <td>Whether to show Restore/View buttons</td>
            <td><code>true</code></td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="events-table">
      <h4>Events</h4>
      <table>
        <thead>
          <tr>
            <th>Event</th>
            <th>Payload</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>fileClick</code></td>
            <td><code>GeneratedFile</code></td>
            <td>Emitted when a file is clicked</td>
          </tr>
          <tr>
            <td><code>restoreVersion</code></td>
            <td><code>FileAccordionVersion</code></td>
            <td>Emitted when Restore button is clicked</td>
          </tr>
          <tr>
            <td><code>viewVersion</code></td>
            <td><code>FileAccordionVersion</code></td>
            <td>Emitted when View button is clicked</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
