import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FileAccordionComponent, FileAccordionVersion } from '../../shared/components/file-accordion/file-accordion.component';
import { GeneratedFile } from '../../shared/models/stepper-states.enum';

@Component({
  selector: 'app-component-demo',
  standalone: true,
  imports: [CommonModule, FileAccordionComponent],
  templateUrl: './component-demo.component.html',
  styleUrls: ['./component-demo.component.scss']
})
export class ComponentDemoComponent {
  currentTheme: 'light' | 'dark' = 'light';
  showActions = true;

  // Sample file versions for demonstration
  fileVersions: FileAccordionVersion[] = [
    {
      version: 'Version 1',
      isExpanded: true,
      files: [
        {
          fileName: 'app/page.tsx',
          status: 'generated',
          path: 'src/app/page.tsx',
          content: 'export default function Page() { return <div>Hello World</div>; }'
        },
        {
          fileName: 'app/loading.tsx',
          status: 'generated',
          path: 'src/app/loading.tsx',
          content: 'export default function Loading() { return <div>Loading...</div>; }'
        },
        {
          fileName: 'components/NavigationMenu.tsx',
          status: 'created',
          path: 'src/components/NavigationMenu.tsx',
          content: 'export default function NavigationMenu() { return <nav>Navigation</nav>; }'
        },
        {
          fileName: 'components/CategoryScroller.tsx',
          status: 'created',
          path: 'src/components/CategoryScroller.tsx',
          content: 'export default function CategoryScroller() { return <div>Categories</div>; }'
        },
        {
          fileName: 'components/OfferBannerCard.tsx',
          status: 'created',
          path: 'src/components/OfferBannerCard.tsx',
          content: 'export default function OfferBannerCard() { return <div>Offer Banner</div>; }'
        }
      ]
    },
    {
      version: 'Version 2',
      isExpanded: false,
      files: [
        {
          fileName: 'app/page.tsx',
          status: 'modified',
          path: 'src/app/page.tsx',
          content: 'export default function Page() { return <div>Updated Hello World</div>; }'
        },
        {
          fileName: 'app/layout.tsx',
          status: 'generated',
          path: 'src/app/layout.tsx',
          content: 'export default function Layout({ children }) { return <html><body>{children}</body></html>; }'
        },
        {
          fileName: 'components/NavigationMenu.tsx',
          status: 'modified',
          path: 'src/components/NavigationMenu.tsx',
          content: 'export default function NavigationMenu() { return <nav className="updated">Navigation</nav>; }'
        },
        {
          fileName: 'components/Footer.tsx',
          status: 'created',
          path: 'src/components/Footer.tsx',
          content: 'export default function Footer() { return <footer>Footer Content</footer>; }'
        }
      ]
    },
    {
      version: 'Version 3',
      isExpanded: false,
      files: [
        {
          fileName: 'pages/HomePage.tsx',
          status: 'generated',
          path: 'src/pages/HomePage.tsx',
          content: 'export default function HomePage() { return <div>Home Page</div>; }'
        },
        {
          fileName: 'pages/ProductPage.tsx',
          status: 'generated',
          path: 'src/pages/ProductPage.tsx',
          content: 'export default function ProductPage() { return <div>Product Page</div>; }'
        },
        {
          fileName: 'pages/CartPage.tsx',
          status: 'modified',
          path: 'src/pages/CartPage.tsx',
          content: 'export default function CartPage() { return <div>Cart Page</div>; }'
        },
        {
          fileName: 'styles/globals.css',
          status: 'created',
          path: 'src/styles/globals.css',
          content: 'body { margin: 0; padding: 0; font-family: Arial, sans-serif; }'
        },
        {
          fileName: 'utils/helpers.ts',
          status: 'created',
          path: 'src/utils/helpers.ts',
          content: 'export function formatDate(date: Date): string { return date.toISOString(); }'
        }
      ]
    }
  ];

  onFileClick(file: GeneratedFile): void {
    console.log('File clicked:', file);
    alert(`File Details:\n\nName: ${file.fileName}\nStatus: ${file.status}\nPath: ${file.path || 'N/A'}\n\nContent Preview:\n${file.content?.substring(0, 100)}...`);
  }

  onRestoreVersion(version: FileAccordionVersion): void {
    console.log('Version restored:', version);
    alert(`Restore Version: ${version.version}\n\nThis would restore ${version.files.length} files to this version state.`);
  }

  onViewVersion(version: FileAccordionVersion): void {
    console.log('Version viewed:', version);
    alert(`View Version: ${version.version}\n\nFiles in this version:\n${version.files.map(f => `• ${f.fileName} (${f.status})`).join('\n')}`);
  }

  toggleTheme(): void {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
  }

  toggleActions(): void {
    this.showActions = !this.showActions;
  }

  addNewVersion(): void {
    const newVersionNumber = this.fileVersions.length + 1;
    const newVersion: FileAccordionVersion = {
      version: `Version ${newVersionNumber}`,
      isExpanded: true,
      files: [
        {
          fileName: `new-file-${newVersionNumber}.tsx`,
          status: 'generated',
          path: `src/new-file-${newVersionNumber}.tsx`,
          content: `// New file content for version ${newVersionNumber}`
        },
        {
          fileName: `updated-component-${newVersionNumber}.tsx`,
          status: 'modified',
          path: `src/components/updated-component-${newVersionNumber}.tsx`,
          content: `// Updated component for version ${newVersionNumber}`
        }
      ]
    };
    
    // Collapse other versions
    this.fileVersions.forEach(v => v.isExpanded = false);
    
    // Add new version at the beginning
    this.fileVersions.unshift(newVersion);
  }

  removeLastVersion(): void {
    if (this.fileVersions.length > 1) {
      this.fileVersions.shift();
    }
  }

  expandAllVersions(): void {
    this.fileVersions.forEach(v => v.isExpanded = true);
  }

  collapseAllVersions(): void {
    this.fileVersions.forEach(v => v.isExpanded = false);
  }

  getComponentCode(): string {
    return `<app-file-accordion
  [versions]="fileVersions"
  [theme]="'${this.currentTheme}'"
  [showActions]="${this.showActions}"
  (fileClick)="onFileClick($event)"
  (restoreVersion)="onRestoreVersion($event)"
  (viewVersion)="onViewVersion($event)">
</app-file-accordion>`;
  }

  getDataStructure(): string {
    return JSON.stringify({
      version: "Version 1",
      isExpanded: true,
      files: [
        {
          fileName: "app/page.tsx",
          status: "generated",
          path: "src/app/page.tsx",
          content: "export default function Page() { return <div>Hello World</div>; }"
        }
      ]
    }, null, 2);
  }
}
