.component-demo-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  &.light {
    background-color: #f8fafc;
    color: #1e293b;
  }
  
  &.dark {
    background-color: #0f172a;
    color: #f1f5f9;
  }
}

.header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
  }

  p {
    font-size: 1.1rem;
    opacity: 0.8;
  }
}

.controls-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  
  .dark & {
    background: #1e293b;
  }

  h3 {
    margin-bottom: 20px;
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.control-group {
  label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.version-controls,
.expand-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  padding: 8px 16px;
  border: 2px solid #3b82f6;
  border-radius: 8px;
  background: white;
  color: #3b82f6;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  
  .dark & {
    background: #1e293b;
    border-color: #60a5fa;
    color: #60a5fa;
  }

  &:hover {
    background: #3b82f6;
    color: white;
    
    .dark & {
      background: #60a5fa;
      color: #1e293b;
    }
  }

  &.small {
    padding: 6px 12px;
    font-size: 0.75rem;
  }
}

.demo-section {
  margin-bottom: 40px;

  h3 {
    margin-bottom: 20px;
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.component-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  
  &.dark {
    background: #1e293b;
  }
}

.code-section {
  margin-bottom: 40px;

  h3 {
    margin-bottom: 20px;
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.code-block {
  background: #1e293b;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  overflow-x: auto;

  h4 {
    color: #f1f5f9;
    margin-bottom: 12px;
    font-size: 1rem;
    font-weight: 600;
  }

  pre {
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;

    code {
      color: #e2e8f0;
      background: none;
      padding: 0;
    }
  }
}

.features-section {
  margin-bottom: 40px;

  h3 {
    margin-bottom: 20px;
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .dark & {
    background: #1e293b;
  }

  h4 {
    margin-bottom: 8px;
    font-size: 1rem;
    font-weight: 600;
  }

  p {
    font-size: 0.875rem;
    opacity: 0.8;
    line-height: 1.5;
    margin: 0;
  }
}

.api-section {
  h3 {
    margin-bottom: 20px;
    font-size: 1.25rem;
    font-weight: 600;
  }

  h4 {
    margin: 20px 0 12px 0;
    font-size: 1.125rem;
    font-weight: 600;
  }
}

.api-table,
.events-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  
  .dark & {
    background: #1e293b;
  }

  table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid #e2e8f0;
      
      .dark & {
        border-bottom-color: #374151;
      }
    }

    th {
      background: #f8fafc;
      font-weight: 600;
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      
      .dark & {
        background: #374151;
      }
    }

    td {
      font-size: 0.875rem;
      
      code {
        background: #f1f5f9;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.75rem;
        
        .dark & {
          background: #374151;
        }
      }
    }

    tr:last-child {
      th,
      td {
        border-bottom: none;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .component-demo-container {
    padding: 15px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .controls-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .version-controls,
  .expand-controls {
    flex-direction: column;
  }

  .api-table,
  .events-table {
    overflow-x: auto;
  }
}
