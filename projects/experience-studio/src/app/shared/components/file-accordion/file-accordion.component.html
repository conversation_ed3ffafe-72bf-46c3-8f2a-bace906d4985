<div class="file-accordion" [ngClass]="theme">
  <div 
    *ngFor="let version of versions; trackBy: trackByVersion" 
    class="accordion-version"
    [class.expanded]="version.isExpanded">
    
    <!-- Version Header -->
    <div class="version-header" (click)="toggleVersion(version)">
      <div class="version-info">
        <!-- Chevron Icon -->
        <div class="chevron-icon" [class.expanded]="version.isExpanded">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        
        <!-- Version Title -->
        <span class="version-title">{{ version.version }}</span>
      </div>
      
      <!-- Action Buttons -->
      <div class="version-actions" *ngIf="showActions">
        <button 
          class="action-btn restore-btn" 
          (click)="onRestoreClick(version, $event)"
          title="Restore this version">
          Restore
        </button>
        <button 
          class="action-btn view-btn" 
          (click)="onViewClick(version, $event)"
          title="View this version">
          View
        </button>
      </div>
    </div>
    
    <!-- Files List -->
    <div class="files-container" *ngIf="version.isExpanded">
      <div class="files-list">
        <div 
          *ngFor="let file of version.files; trackBy: trackByFile" 
          class="file-item"
          (click)="onFileClick(file)">
          
          <!-- File Icon -->
          <div class="file-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="3" fill="currentColor"/>
            </svg>
          </div>
          
          <!-- File Name -->
          <span class="file-name">{{ file.fileName }}</span>
          
          <!-- File Status -->
          <span class="file-status" [ngClass]="getStatusClass(file.status)">
            {{ getStatusText(file.status) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
