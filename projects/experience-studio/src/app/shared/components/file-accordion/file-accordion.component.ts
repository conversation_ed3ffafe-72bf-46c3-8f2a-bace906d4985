import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { GeneratedFile } from '../../models/stepper-states.enum';

export interface FileAccordionVersion {
  version: string;
  files: GeneratedFile[];
  isExpanded?: boolean;
}

@Component({
  selector: 'app-file-accordion',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './file-accordion.component.html',
  styleUrls: ['./file-accordion.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FileAccordionComponent {
  @Input() versions: FileAccordionVersion[] = [];
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() showActions: boolean = true;

  @Output() fileClick = new EventEmitter<GeneratedFile>();
  @Output() restoreVersion = new EventEmitter<FileAccordionVersion>();
  @Output() viewVersion = new EventEmitter<FileAccordionVersion>();

  /**
   * Toggle the expansion state of a version
   */
  toggleVersion(version: FileAccordionVersion): void {
    version.isExpanded = !version.isExpanded;
  }

  /**
   * Handle file click event
   */
  onFileClick(file: GeneratedFile): void {
    this.fileClick.emit(file);
  }

  /**
   * Handle restore button click
   */
  onRestoreClick(version: FileAccordionVersion, event: Event): void {
    event.stopPropagation();
    this.restoreVersion.emit(version);
  }

  /**
   * Handle view button click
   */
  onViewClick(version: FileAccordionVersion, event: Event): void {
    event.stopPropagation();
    this.viewVersion.emit(version);
  }

  /**
   * Get the appropriate icon for a file based on its extension
   */
  getFileIcon(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'ts':
      case 'tsx':
        return '📘';
      case 'js':
      case 'jsx':
        return '📙';
      case 'css':
      case 'scss':
      case 'sass':
        return '🎨';
      case 'html':
        return '🌐';
      case 'json':
        return '📋';
      case 'md':
        return '📝';
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return '🖼️';
      default:
        return '📄';
    }
  }

  /**
   * Get status badge class for file status
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'generated':
        return 'status-generated';
      case 'modified':
        return 'status-modified';
      case 'created':
        return 'status-created';
      default:
        return 'status-default';
    }
  }

  /**
   * Get status display text
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'generated':
        return 'Generated';
      case 'modified':
        return 'Modified';
      case 'created':
        return 'Created';
      default:
        return 'Unknown';
    }
  }

  /**
   * Track by function for versions
   */
  trackByVersion(index: number, version: FileAccordionVersion): string {
    return version.version;
  }

  /**
   * Track by function for files
   */
  trackByFile(index: number, file: GeneratedFile): string {
    return file.fileName;
  }
}
