.file-accordion {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  
  &.light {
    background-color: var(--chat-window-card-bg-color, #ffffff);
    border: 1px solid var(--border-color, #e2e8f0);
  }
  
  &.dark {
    background-color: var(--chat-window-card-bg-color-dark, #1a1a1a);
    border: 1px solid var(--border-color-dark, #374151);
  }
}

.accordion-version {
  border-bottom: 1px solid;
  transition: all 0.3s ease;
  
  .light & {
    border-bottom-color: var(--border-color, #e2e8f0);
  }
  
  .dark & {
    border-bottom-color: var(--border-color-dark, #374151);
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  &.expanded {
    .chevron-icon {
      transform: rotate(90deg);
    }
  }
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  .light & {
    background-color: var(--chat-window-card-bg-color, #ffffff);
    
    &:hover {
      background-color: var(--hover-bg-color, #f8fafc);
    }
  }
  
  .dark & {
    background-color: var(--chat-window-card-bg-color-dark, #1a1a1a);
    
    &:hover {
      background-color: var(--hover-bg-color-dark, #2d3748);
    }
  }
}

.version-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chevron-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
  
  svg {
    .light & {
      color: var(--text-secondary, #64748b);
    }
    
    .dark & {
      color: var(--text-secondary-dark, #9ca3af);
    }
  }
}

.version-title {
  font-size: 16px;
  font-weight: 600;
  
  .light & {
    color: var(--text-primary, #1e293b);
  }
  
  .dark & {
    color: var(--text-primary-dark, #f1f5f9);
  }
}

.version-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.restore-btn {
    .light & {
      background-color: transparent;
      border-color: var(--border-color, #e2e8f0);
      color: var(--text-secondary, #64748b);
      
      &:hover {
        background-color: var(--hover-bg-color, #f8fafc);
        border-color: var(--color-primary, #6366f1);
        color: var(--color-primary, #6366f1);
      }
    }
    
    .dark & {
      background-color: transparent;
      border-color: var(--border-color-dark, #374151);
      color: var(--text-secondary-dark, #9ca3af);
      
      &:hover {
        background-color: var(--hover-bg-color-dark, #2d3748);
        border-color: var(--color-primary-light, #818cf8);
        color: var(--color-primary-light, #818cf8);
      }
    }
  }
  
  &.view-btn {
    .light & {
      background-color: var(--color-primary, #6366f1);
      border-color: var(--color-primary, #6366f1);
      color: white;
      
      &:hover {
        background-color: var(--color-primary-dark, #4f46e5);
        border-color: var(--color-primary-dark, #4f46e5);
      }
    }
    
    .dark & {
      background-color: var(--color-primary-light, #818cf8);
      border-color: var(--color-primary-light, #818cf8);
      color: var(--text-primary-dark, #1e293b);
      
      &:hover {
        background-color: var(--color-primary, #6366f1);
        border-color: var(--color-primary, #6366f1);
      }
    }
  }
}

.files-container {
  .light & {
    background-color: var(--bg-secondary, #f8fafc);
  }
  
  .dark & {
    background-color: var(--bg-secondary-dark, #111827);
  }
}

.files-list {
  padding: 8px 0;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 20px 8px 52px; // Indent to align with version content
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  .light & {
    &:hover {
      background-color: var(--hover-bg-color, #f1f5f9);
    }
  }
  
  .dark & {
    &:hover {
      background-color: var(--hover-bg-color-dark, #1f2937);
    }
  }
}

.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  
  svg {
    .light & {
      color: var(--color-primary, #6366f1);
    }
    
    .dark & {
      color: var(--color-primary-light, #818cf8);
    }
  }
}

.file-name {
  flex: 1;
  font-size: 14px;
  font-weight: 400;
  
  .light & {
    color: var(--text-primary, #1e293b);
  }
  
  .dark & {
    color: var(--text-primary-dark, #f1f5f9);
  }
}

.file-status {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  
  &.status-generated {
    .light & {
      background-color: #dcfce7;
      color: #166534;
    }
    
    .dark & {
      background-color: #14532d;
      color: #bbf7d0;
    }
  }
  
  &.status-modified {
    .light & {
      background-color: #fef3c7;
      color: #92400e;
    }
    
    .dark & {
      background-color: #451a03;
      color: #fde68a;
    }
  }
  
  &.status-created {
    .light & {
      background-color: #dbeafe;
      color: #1e40af;
    }
    
    .dark & {
      background-color: #1e3a8a;
      color: #bfdbfe;
    }
  }
  
  &.status-default {
    .light & {
      background-color: #f1f5f9;
      color: #64748b;
    }
    
    .dark & {
      background-color: #374151;
      color: #9ca3af;
    }
  }
}
